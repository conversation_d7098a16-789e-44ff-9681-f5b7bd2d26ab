### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### Test Flow Oluşturma
# @name createTestFlow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Transition Test Workflow",
    "Description": "Transition testleri için workflow"
}

###

@flowId = {{createTestFlow.response.body.Value}}

### Node 1 Oluşturma - Başlangıç
# @name createNode1
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "Description": "<PERSON><PERSON><PERSON><PERSON> başlangıç noktası",
    "NodeType": 1
}

###

@node1Id = {{createNode1.response.body.Value}}

### Node 2 Oluşturma - İşlem
# @name createNode2
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "İşlem",
    "Description": "Ana işlem aşaması",
    "NodeType": 2
}

###

@node2Id = {{createNode2.response.body.Value}}

### Node 3 Oluşturma - Bitiş
# @name createNode3
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Bitiş",
    "Description": "Sürecin bitiş noktası",
    "NodeType": 3
}

###

@node3Id = {{createNode3.response.body.Value}}

### Node 4 Oluşturma - Alternatif
# @name createNode4
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Alternatif",
    "Description": "Alternatif işlem yolu",
    "NodeType": 2
}

###

@node4Id = {{createNode4.response.body.Value}}

### Transition Oluşturma - Başlangıç -> İşlem
# @name createTransition1
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node1Id}}",
    "ToNodeId": "{{node2Id}}",
    "Name": "Başlat"
}

###

@transition1Id = {{createTransition1.response.body.Value}}

### Transition Oluşturma - İşlem -> Bitiş
# @name createTransition2
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node2Id}}",
    "ToNodeId": "{{node3Id}}",
    "Name": "Tamamla"
}

###

@transition2Id = {{createTransition2.response.body.Value}}

### Transition Oluşturma - Başlangıç -> Alternatif
# @name createTransition3
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node1Id}}",
    "ToNodeId": "{{node4Id}}",
    "Name": "Alternatif Yol"
}

###

@transition3Id = {{createTransition3.response.body.Value}}

### Transition Oluşturma - Alternatif -> Bitiş
# @name createTransition4
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node4Id}}",
    "ToNodeId": "{{node3Id}}",
    "Name": "Alternatif Tamamla"
}

###

@transition4Id = {{createTransition4.response.body.Value}}

### Transition Listesi - Tümü
GET {{baseUrl}}/api/v1/requests/transitions?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Transition Listesi - FromNode'a göre
GET {{baseUrl}}/api/v1/requests/transitions?FromNodeId={{node1Id}}&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Transition Listesi - ToNode'a göre
GET {{baseUrl}}/api/v1/requests/transitions?ToNodeId={{node3Id}}&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Transition Listesi - Arama ile
GET {{baseUrl}}/api/v1/requests/transitions?SearchTerm=Tamamla&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Transition Detayı
GET {{baseUrl}}/api/v1/requests/transitions/{{transition1Id}}
Authorization: Bearer {{token}}

### Transition Güncelleme
PUT {{baseUrl}}/api/v1/requests/transitions/{{transition1Id}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Süreci Başlat"
}

###

### İkinci Test Flow Oluşturma
# @name createSecondFlow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "İkinci Transition Test Flow",
    "Description": "Farklı flow testleri için"
}

###

@secondFlowId = {{createSecondFlow.response.body.Value}}

### İkinci Flow için Node Oluşturma
# @name createSecondFlowNode1
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{secondFlowId}}",
    "Name": "İkinci Flow Node 1",
    "Description": "İkinci flow'un ilk node'u",
    "NodeType": 1
}

###

@secondFlowNode1Id = {{createSecondFlowNode1.response.body.Value}}

### İkinci Flow için Node Oluşturma
# @name createSecondFlowNode2
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{secondFlowId}}",
    "Name": "İkinci Flow Node 2",
    "Description": "İkinci flow'un ikinci node'u",
    "NodeType": 3
}

###

@secondFlowNode2Id = {{createSecondFlowNode2.response.body.Value}}

### Hata Durumu Testleri

### Olmayan Node'lar arası Transition oluşturma
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "10000000-0000-0000-0000-000000000000",
    "ToNodeId": "{{node2Id}}",
    "Name": "Olmayan Kaynak"
}

### Farklı Flow'lardaki Node'lar arası Transition oluşturma (hata vermeli)
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node1Id}}",
    "ToNodeId": "{{secondFlowNode1Id}}",
    "Name": "Farklı Flow"
}

### Kendine Transition oluşturma (hata vermeli)
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node1Id}}",
    "ToNodeId": "{{node1Id}}",
    "Name": "Kendine Döngü"
}

### Döngüsel bağımlılık oluşturma (hata vermeli) - Bitiş -> Başlangıç
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node3Id}}",
    "ToNodeId": "{{node1Id}}",
    "Name": "Döngü Oluştur"
}

### Aynı node'lar arası aynı isimde Transition oluşturma (hata vermeli)
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node1Id}}",
    "ToNodeId": "{{node2Id}}",
    "Name": "Süreci Başlat"
}

### Geçersiz veri ile Transition oluşturma (boş name)
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{node2Id}}",
    "ToNodeId": "{{node4Id}}",
    "Name": ""
}

### Olmayan Transition detayı
GET {{baseUrl}}/api/v1/requests/transitions/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Olmayan Transition güncelleme
PUT {{baseUrl}}/api/v1/requests/transitions/10000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Olmayan Transition"
}

### Transition Silme - Transition 4
DELETE {{baseUrl}}/api/v1/requests/transitions/{{transition4Id}}
Authorization: Bearer {{token}}

### Transition Silme - Transition 3
DELETE {{baseUrl}}/api/v1/requests/transitions/{{transition3Id}}
Authorization: Bearer {{token}}

### Transition Silme - Transition 2
DELETE {{baseUrl}}/api/v1/requests/transitions/{{transition2Id}}
Authorization: Bearer {{token}}

### Transition Silme - Transition 1
DELETE {{baseUrl}}/api/v1/requests/transitions/{{transition1Id}}
Authorization: Bearer {{token}}

### Olmayan Transition silme
DELETE {{baseUrl}}/api/v1/requests/transitions/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Test Verilerini Temizleme

### İkinci Flow'u Temizle
DELETE {{baseUrl}}/api/v1/requests/flows/{{secondFlowId}}
Authorization: Bearer {{token}}

### Ana Test Flow'u Temizle
DELETE {{baseUrl}}/api/v1/requests/flows/{{flowId}}
Authorization: Bearer {{token}}

### Son Transition Listesi - Silme işlemlerini kontrol et
GET {{baseUrl}}/api/v1/requests/transitions?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}
