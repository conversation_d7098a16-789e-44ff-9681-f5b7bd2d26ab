### Variables
@baseUrl = https://localhost:7001
@transitionId = 550e8400-e29b-41d4-a716-446655440000
@ruleId = 550e8400-e29b-41d4-a716-446655440001
@ticketId = 550e8400-e29b-41d4-a716-446655440002

### Create Authorization Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Admin Only Authorization",
  "description": "Sadece admin kullanıcılar bu transition'ı yapabilir",
  "ruleType": 0,
  "order": 1,
  "isActive": true,
  "configuration": "{\"requiredRoles\":[\"Admin\"],\"requiredUsers\":[],\"departmentRestrictions\":[]}"
}

### Create Field Validation Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Title Required Validation",
  "description": "Başlık alanı zorunlu kontrolü",
  "ruleType": 1,
  "order": 2,
  "isActive": true,
  "configuration": "{\"validationRules\":[{\"fieldName\":\"Title\",\"validationType\":0,\"isRequired\":true,\"errorMessage\":\"Başlık alanı zorunludur\"}]}"
}

### Create Field Modification Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Set End Date",
  "description": "Bitiş tarihini otomatik olarak ayarla",
  "ruleType": 2,
  "order": 3,
  "isActive": true,
  "configuration": "{\"modifications\":[{\"fieldName\":\"EndDate\",\"type\":1,\"value\":null}]}"
}

### Create Notification Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Notify Admin",
  "description": "Admin'e bildirim gönder",
  "ruleType": 3,
  "order": 4,
  "isActive": true,
  "configuration": "{\"notifications\":[{\"type\":0,\"recipients\":{\"type\":1,\"roles\":[\"Admin\"]},\"template\":\"transition-notification\",\"subject\":\"Ticket {{TicketId}} durumu değişti\",\"condition\":\"\",\"delaySeconds\":0}]}"
}

### Get Rule by ID
GET {{baseUrl}}/api/v1/requests/rules/{{ruleId}}

### List Rules by Transition
GET {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules?pageNumber=1&pageSize=10

### List All Rules with Filters
GET {{baseUrl}}/api/v1/requests/rules?ruleType=0&isActive=true&pageNumber=1&pageSize=10

### Update Rule
PUT {{baseUrl}}/api/v1/requests/rules/{{ruleId}}
Content-Type: application/json

{
  "name": "Updated Admin Authorization",
  "description": "Güncellenmiş admin yetkilendirme kuralı",
  "ruleType": 0,
  "order": 1,
  "isActive": true,
  "configuration": "{\"requiredRoles\":[\"Admin\",\"Manager\"],\"requiredUsers\":[],\"departmentRestrictions\":[]}"
}

### Execute Transition with Rules
POST {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/transitions/{{transitionId}}/execute
Content-Type: application/json

{
  "additionalData": {
    "comment": "Transition executed via API test",
    "priority": "High"
  }
}

### Delete Rule
DELETE {{baseUrl}}/api/v1/requests/rules/{{ruleId}}

### Complex Field Validation Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Complex Validation",
  "description": "Karmaşık alan doğrulama kuralları",
  "ruleType": 1,
  "order": 5,
  "isActive": true,
  "configuration": "{\"validationRules\":[{\"fieldName\":\"Title\",\"validationType\":1,\"minLength\":5,\"errorMessage\":\"Başlık en az 5 karakter olmalı\"},{\"fieldName\":\"Description\",\"validationType\":2,\"maxLength\":1000,\"errorMessage\":\"Açıklama en fazla 1000 karakter olabilir\"},{\"fieldName\":\"Email\",\"validationType\":3,\"pattern\":\"^[\\\\w-\\\\.]+@([\\\\w-]+\\\\.)+[\\\\w-]{2,4}$\",\"errorMessage\":\"Geçerli bir email adresi giriniz\"}]}"
}

### Complex Field Modification Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Complex Modifications",
  "description": "Karmaşık alan değiştirme kuralları",
  "ruleType": 2,
  "order": 6,
  "isActive": true,
  "configuration": "{\"modifications\":[{\"fieldName\":\"UserId\",\"type\":2,\"value\":null},{\"fieldName\":\"UpdatedAt\",\"type\":1,\"value\":null},{\"fieldName\":\"Status\",\"type\":0,\"value\":\"In Progress\"},{\"fieldName\":\"Comment\",\"type\":3,\"value\":\"Ticket updated by {{USER.Name}} at {{NOW}}\"}]}"
}

### Complex Notification Rule
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Multi Notification",
  "description": "Çoklu bildirim kuralı",
  "ruleType": 3,
  "order": 7,
  "isActive": true,
  "configuration": "{\"notifications\":[{\"type\":0,\"recipients\":{\"type\":3,\"users\":[]},\"template\":\"current-user-notification\",\"subject\":\"Ticket {{TicketId}} işleminiz tamamlandı\",\"condition\":\"\",\"delaySeconds\":0},{\"type\":1,\"recipients\":{\"type\":0,\"fieldName\":\"UserId\"},\"template\":\"assigned-user-notification\",\"subject\":\"Size atanan ticket {{TicketId}} güncellendi\",\"condition\":\"Priority == 'High'\",\"delaySeconds\":300}]}"
}

### Test Authorization Rule with Different Roles
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "Role Based Authorization",
  "description": "Rol tabanlı yetkilendirme testi",
  "ruleType": 0,
  "order": 8,
  "isActive": true,
  "configuration": "{\"requiredRoles\":[\"Manager\",\"TeamLead\"],\"requiredUsers\":[\"550e8400-e29b-41d4-a716-446655440003\"],\"departmentRestrictions\":[\"IT\",\"Support\"]}"
}

### Test Field Validation with All Types
POST {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules
Content-Type: application/json

{
  "name": "All Validation Types",
  "description": "Tüm doğrulama tiplerini test et",
  "ruleType": 1,
  "order": 9,
  "isActive": true,
  "configuration": "{\"validationRules\":[{\"fieldName\":\"Priority\",\"validationType\":4,\"value\":\"High\",\"errorMessage\":\"Priority High olmalı\"},{\"fieldName\":\"Status\",\"validationType\":5,\"value\":\"Closed\",\"errorMessage\":\"Status Closed olmamalı\"},{\"fieldName\":\"Title\",\"validationType\":0,\"isRequired\":true},{\"fieldName\":\"Description\",\"validationType\":1,\"minLength\":10},{\"fieldName\":\"Comment\",\"validationType\":2,\"maxLength\":500}]}"
}

### List Rules with Pagination Test
GET {{baseUrl}}/api/v1/requests/transitions/{{transitionId}}/rules?pageNumber=2&pageSize=5

### List Rules by Type
GET {{baseUrl}}/api/v1/requests/rules?ruleType=1&pageNumber=1&pageSize=20

### List Inactive Rules
GET {{baseUrl}}/api/v1/requests/rules?isActive=false&pageNumber=1&pageSize=10

### Execute Transition with Complex Data
POST {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/transitions/{{transitionId}}/execute
Content-Type: application/json

{
  "additionalData": {
    "title": "Updated Ticket Title",
    "description": "This is a detailed description for testing field modifications",
    "priority": "High",
    "assignedUser": "550e8400-e29b-41d4-a716-446655440004",
    "tags": ["urgent", "customer-request"],
    "metadata": {
      "source": "api-test",
      "version": "1.0"
    }
  }
}
