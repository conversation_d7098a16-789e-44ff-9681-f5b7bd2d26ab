using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Shared.Infrastructure.Localization;

namespace Conversations.Application.Notes.ListAllNotes;

public class ListAllNotesQueryHandler(
    IConversationDbContext dbContext,
    ISharedUserService userService,
    ISharedCustomerService customerService,
    ILocalizer localizer,
    IWorkContext workContext
) : IRequestHandler<ListAllNotesQuery, PagedResult<NoteAllDto>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedCustomerService _customerService = customerService;
    private readonly ILocalizer _localizer = localizer;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResult<NoteAllDto>> Handle(ListAllNotesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.CallNote
            .Include(x => x.Call)
            .Where(x => !string.IsNullOrEmpty(x.Content))
            .AsNoTracking();
        var userId = _workContext.UserId;
        var hasPermission = await _userService.HasPermissionAsync(userId, "Conversations.Notes.ViewAll");
        if (!_workContext.HasRole("Admin") || hasPermission)
        {
            query = query.Where(p => p.InsertUserId == userId);
        }

        if (request.CallId.HasValue)
        {
            query = query.Where(x => x.CallId == request.CallId);
        }
        if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
        {
            query = query.Where(x => x.Call.Phone.Contains(request.PhoneNumber));
        }
        if (request.CustomerId.HasValue)
        {
            query = query.Where(x => x.CustomerId == request.CustomerId || x.Call.CustomerId == request.CustomerId);
        }
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(x => x.Content.Contains(request.SearchTerm));
        }
        if (request.StartDate.HasValue)
        {
            query = query.Where(x => x.InsertDate.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            query = query.Where(x => x.InsertDate.Date <= request.EndDate.Value.Date);
        }
        if (request.InsertUserId.HasValue)
        {
            query = query.Where(x => x.InsertUserId == request.InsertUserId);
        }
        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await _dbContext.CallNote.CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(x => x.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new NoteAllDto(
                x.Id,
                x.CallId,
                x.CustomerId,
                x.Content,
                x.InsertDate,
                x.InsertUserId,
                x.Call.Phone))
            .ToListAsync(cancellationToken);
        List<Guid> userIds = [.. items.Where(x => x.InsertUserId != null).Select(x => x.InsertUserId ?? Guid.Empty)];
        var users = await _userService.GetUsersByIdsAsync(userIds);
        var userDict = users.ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}");
        var customerResult = await _customerService.GetCustomerByIdsAsync(items.Select(x => x.CustomerId ?? Guid.Empty).Distinct().ToList());
        var customerDict = customerResult.IsSuccess && customerResult.Value != null
            ? customerResult.Value.ToDictionary(c => c.Id, c => c.Name)
            : new Dictionary<Guid, string>();

        foreach (var item in items)
        {
            item.InsertUser = userDict.ContainsKey(item.InsertUserId ?? Guid.Empty) ? userDict[item.InsertUserId ?? Guid.Empty] : _localizer.Get("UnknownUser");
            item.CustomerName = customerDict.ContainsKey(item.CustomerId ?? Guid.Empty) ? customerDict[item.CustomerId ?? Guid.Empty] : "";
        }
        return new PagedResult<NoteAllDto>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }
}
