using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class TransitionRuleConfiguration : IEntityTypeConfiguration<TransitionRule>
{
    public void Configure(EntityTypeBuilder<TransitionRule> builder)
    {
        builder.ToTable("TransitionRules");

        builder.HasKey(tr => tr.Id);

        builder.Property(tr => tr.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(tr => tr.Description)
            .HasMaxLength(1000);

        builder.Property(tr => tr.RuleType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(tr => tr.Order)
            .IsRequired();

        builder.Property(tr => tr.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(tr => tr.Configuration)
            .IsRequired()
            .HasDefaultValue("{}");

        builder.HasOne(tr => tr.Transition)
            .WithMany(t => t.Rules)
            .HasForeignKey(tr => tr.TransitionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(tr => tr.RuleExecutions)
            .WithOne(re => re.TransitionRule)
            .HasForeignKey(re => re.TransitionRuleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(tr => new { tr.TransitionId, tr.Order })
            .IsUnique();

        builder.HasIndex(tr => tr.IsActive);
    }
}
