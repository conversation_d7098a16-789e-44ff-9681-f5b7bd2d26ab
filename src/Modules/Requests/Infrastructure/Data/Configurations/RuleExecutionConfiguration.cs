using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class RuleExecutionConfiguration : IEntityTypeConfiguration<RuleExecution>
{
    public void Configure(EntityTypeBuilder<RuleExecution> builder)
    {
        builder.ToTable("RuleExecutions");

        builder.<PERSON><PERSON>ey(re => re.Id);

        builder.Property(re => re.ExecutedAt)
            .IsRequired();

        builder.Property(re => re.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(re => re.InputData)
            .IsRequired()
            .HasDefaultValue(string.Empty);

        builder.Property(re => re.OutputData)
            .IsRequired()
            .HasDefaultValue(string.Empty);

        builder.Property(re => re.ErrorMessage)
            .IsRequired()
            .HasDefaultValue(string.Empty);

        builder.Property(re => re.ExecutionTimeMs)
            .IsRequired();

        builder.HasOne(re => re.TransitionRule)
            .WithMany(tr => tr.RuleExecutions)
            .HasForeignKey(re => re.TransitionRuleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(re => re.TicketId);
        builder.HasIndex(re => re.ExecutedByUserId);
        builder.HasIndex(re => re.ExecutedAt);
        builder.HasIndex(re => re.Status);
    }
}
