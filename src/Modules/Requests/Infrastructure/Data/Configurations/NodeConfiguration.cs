using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class NodeConfiguration : IEntityTypeConfiguration<Node>
{
    public void Configure(EntityTypeBuilder<Node> builder)
    {
        builder.ToTable("Nodes");

        builder.<PERSON><PERSON>ey(n => n.Id);

        builder.Property(n => n.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(n => n.Description)
            .HasMaxLength(500);

        builder.Property(n => n.NodeType)
            .HasConversion<int>();

        builder.HasOne(n => n.Flow)
            .WithMany(f => f.Nodes)
            .HasForeignKey(n => n.FlowId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(n => n.FromTransitions)
            .WithOne(t => t.FromNode)
            .HasForeignKey(t => t.FromNodeId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasMany(n => n.ToTransitions)
            .WithOne(t => t.ToNode)
            .HasForeignKey(t => t.ToNodeId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasIndex(n => new { n.FlowId, n.Name })
            .IsUnique();
    }
}
