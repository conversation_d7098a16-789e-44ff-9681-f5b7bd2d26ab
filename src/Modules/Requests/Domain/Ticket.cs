using Shared.Domain;

namespace Requests.Domain;

public class Ticket : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid SubjectId { get; set; }
    public TicketSubject? Subject { get; set; }
    public Guid CustomerId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public Guid? NotificationWayId { get; set; }
    public Guid? UserId { get; set; }
    public Guid ReporterUserId { get; set; }
    public List<TicketDepartment> TicketDepartment { get; set; } = [];
    public List<TicketFile> TicketFiles { get; set; } = [];
    public PriorityEnum Priority { get; set; }
    public Guid StatusId { get; set; }
    public DateTime? EndDate { get; set; }
    public List<Guid> Watchlist { get; set; } = []; // Takip eden kullanıcılar
}
