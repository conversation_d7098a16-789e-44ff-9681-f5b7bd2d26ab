using Shared.Domain;

namespace Requests.Domain;

public class Node : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid FlowId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public NodeType NodeType { get; set; }
    public Flow Flow { get; set; } = null!;
    public ICollection<Transition> FromTransitions { get; set; } = [];
    public ICollection<Transition> ToTransitions { get; set; } = [];
}
