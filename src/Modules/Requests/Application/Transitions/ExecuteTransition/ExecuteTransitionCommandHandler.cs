using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Requests.Application.Abstractions;
using Requests.Application.Rules.Engine;
using Shared.Application;
using Shared.Infrastructure.Context;

namespace Requests.Application.Transitions.ExecuteTransition;

public class ExecuteTransitionCommandHandler(
    IRequestsDbContext context,
    IRuleEngine ruleEngine,
    IMediator mediator,
    IWorkContext workContext,
    ILogger<ExecuteTransitionCommandHandler> logger) : IRequestHandler<ExecuteTransitionCommand, Result<TransitionExecutionResult>>
{
    public async Task<Result<TransitionExecutionResult>> Handle(
        ExecuteTransitionCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            // 1. Transition bilgilerini al
            var transition = await context.Transitions
                .Include(t => t.FromNode)
                .Include(t => t.ToNode)
                .Include(t => t.Rules.Where(r => r.IsActive))
                .FirstOrDefaultAsync(t => t.Id == request.TransitionId, cancellationToken);

            if (transition == null)
                return Result.Failure<TransitionExecutionResult>("Transition bulunamadı");

            // 2. Ticket'ın var olduğunu kontrol et
            var ticket = await context.Tickets
                .FirstOrDefaultAsync(t => t.Id == request.TicketId, cancellationToken);

            if (ticket == null)
                return Result.Failure<TransitionExecutionResult>("Ticket bulunamadı");

            // 3. Rich Context oluştur
            var transitionContext = await CreateTransitionContextAsync(
                request.TicketId,
                transition,
                request.AdditionalData,
                cancellationToken);

            // 4. Rule Engine ile kuralları çalıştır
            var ruleResult = await ruleEngine.ExecuteRulesAsync(
                request.TransitionId,
                transitionContext,
                cancellationToken);

            if (!ruleResult.IsSuccess)
            {
                return Result.Failure<TransitionExecutionResult>(
                    string.Join(", ", ruleResult.Errors));
            }

            // 5. Ticket'i güncelle
            await UpdateTicketAsync(
                request.TicketId,
                transition.ToNode.Id,
                ruleResult.ModifiedFields,
                cancellationToken);

            // 6. Bildirimleri gönder (şimdilik log)
            foreach (var notification in ruleResult.PendingNotifications)
            {
                logger.LogInformation("Notification queued: {Type} to {RecipientCount} recipients", 
                    notification.Type, notification.Recipients.Count);
                // TODO: Notification service ile bildirim gönder
                // await mediator.Send(new SendNotificationCommand(notification), cancellationToken);
            }

            return Result.Success(new TransitionExecutionResult
            {
                TransitionId = request.TransitionId,
                NewNodeId = transition.ToNodeId,
                ModifiedFields = ruleResult.ModifiedFields,
                NotificationsSent = ruleResult.PendingNotifications.Count,
                ExecutedRules = ruleResult.ExecutedRuleCount
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Transition execution failed for ticket {TicketId} and transition {TransitionId}", 
                request.TicketId, request.TransitionId);
            return Result.Failure<TransitionExecutionResult>("Transition çalıştırma sırasında hata oluştu");
        }
    }

    private async Task<TransitionContext> CreateTransitionContextAsync(
        Guid ticketId,
        Domain.Transition transition,
        Dictionary<string, object>? additionalData,
        CancellationToken cancellationToken)
    {
        // Ticket verilerini al
        var ticket = await context.Tickets
            .FirstOrDefaultAsync(t => t.Id == ticketId, cancellationToken);

        var ticketData = new Dictionary<string, object>();
        if (ticket != null)
        {
            ticketData["Id"] = ticket.Id;
            ticketData["Title"] = ticket.Title;
            ticketData["Description"] = ticket.Description;
            ticketData["Priority"] = ticket.Priority.ToString();
            ticketData["StatusId"] = ticket.StatusId;
            ticketData["CustomerId"] = ticket.CustomerId;
            ticketData["UserId"] = ticket.UserId;
            ticketData["ReporterUserId"] = ticket.ReporterUserId;
        }

        // User verilerini al (şimdilik basit)
        var userData = new Dictionary<string, object>
        {
            ["Id"] = workContext.UserId,
            ["Name"] = "Current User" // TODO: User service'den gerçek veri al
        };

        return new TransitionContext
        {
            TicketId = ticketId,
            UserId = workContext.UserId,
            OrganizationId = workContext.OrganizationId ?? Guid.Empty,
            TicketData = ticketData,
            UserData = userData,
            Metadata = additionalData ?? new(),
            FromNode = transition.FromNode,
            ToNode = transition.ToNode,
            TransitionTime = DateTime.UtcNow
        };
    }

    private async Task UpdateTicketAsync(
        Guid ticketId,
        Guid newNodeId,
        Dictionary<string, object> modifiedFields,
        CancellationToken cancellationToken)
    {
        var ticket = await context.Tickets
            .FirstOrDefaultAsync(t => t.Id == ticketId, cancellationToken);

        if (ticket == null) return;

        // StatusId'yi güncelle (node'un ID'si status olarak kullanılıyor)
        ticket.StatusId = newNodeId;

        // Modified fields'ları uygula
        foreach (var field in modifiedFields)
        {
            switch (field.Key.ToLower())
            {
                case "title":
                    ticket.Title = field.Value?.ToString() ?? ticket.Title;
                    break;
                case "description":
                    ticket.Description = field.Value?.ToString() ?? ticket.Description;
                    break;
                case "priority":
                    if (Enum.TryParse<Domain.PriorityEnum>(field.Value?.ToString(), out var priority))
                        ticket.Priority = priority;
                    break;
                case "userid":
                    if (Guid.TryParse(field.Value?.ToString(), out var userId))
                        ticket.UserId = userId;
                    break;
                case "enddate":
                    if (DateTime.TryParse(field.Value?.ToString(), out var endDate))
                        ticket.EndDate = endDate;
                    break;
            }
        }

        await context.SaveChangesAsync(cancellationToken);
    }
}
