using FluentValidation;

namespace Requests.Application.Transitions.ExecuteTransition;

public class ExecuteTransitionCommandValidator : AbstractValidator<ExecuteTransitionCommand>
{
    public ExecuteTransitionCommandValidator()
    {
        RuleFor(x => x.TicketId)
            .NotEmpty()
            .WithMessage("Ticket ID gereklidir");

        RuleFor(x => x.TransitionId)
            .NotEmpty()
            .WithMessage("Transition ID gereklidir");
    }
}
