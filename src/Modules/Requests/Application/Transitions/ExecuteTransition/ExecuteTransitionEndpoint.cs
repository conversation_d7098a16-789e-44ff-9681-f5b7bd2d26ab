using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Transitions.ExecuteTransition;

public class ExecuteTransitionEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/tickets/{ticketId}/transitions/{transitionId}/execute", async (
            Guid ticketId,
            Guid transitionId,
            ExecuteTransitionRequest? request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new ExecuteTransitionCommand(
                ticketId,
                transitionId,
                request?.AdditionalData
            );
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Transitions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Transitions");
    }
}

public record ExecuteTransitionRequest(
    Dictionary<string, object>? AdditionalData = null
);
