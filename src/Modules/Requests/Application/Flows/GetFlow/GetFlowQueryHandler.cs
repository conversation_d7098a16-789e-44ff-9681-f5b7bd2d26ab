using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Flows.GetFlow;

public class GetFlowQueryHandler(IRequestsDbContext context)
    : IRequestHandler<GetFlowQuery, Result<FlowDto>>
{
    public async Task<Result<FlowDto>> Handle(GetFlowQuery request, CancellationToken cancellationToken)
    {
        var flow = await context.Flows
            .Include(f => f.Nodes)
                .ThenInclude(n => n.FromTransitions)
            .Include(f => f.Nodes)
                .ThenInclude(n => n.ToTransitions)
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (flow is null)
        {
            return Result.Failure<FlowDto>("Flow bulunamadı");
        }

        var flowDto = new FlowDto
        {
            Id = flow.Id,
            Name = flow.Name,
            Description = flow.Description,
            InsertDate = flow.InsertDate,
            UpdateDate = flow.UpdateDate,
            Nodes = flow.Nodes.Select(n => new NodeDto
            {
                Id = n.Id,
                FlowId = n.FlowId,
                FlowName = flow.Name,
                Name = n.Name,
                Description = n.Description,
                Type = n.NodeType.ToString(),
                InsertDate = n.InsertDate,
                UpdateDate = n.UpdateDate,
                FromTransitions = n.FromTransitions.Select(t => new TransitionDto
                {
                    Id = t.Id,
                    FromNodeId = t.FromNodeId,
                    ToNodeId = t.ToNodeId,
                    Name = t.Name,
                    InsertDate = t.InsertDate,
                    UpdateDate = t.UpdateDate
                }).ToList(),
                ToTransitions = n.ToTransitions.Select(t => new TransitionDto
                {
                    Id = t.Id,
                    FromNodeId = t.FromNodeId,
                    ToNodeId = t.ToNodeId,
                    Name = t.Name,
                    InsertDate = t.InsertDate,
                    UpdateDate = t.UpdateDate
                }).ToList()
            }).ToList()
        };

        return Result.Success(flowDto);
    }
}
