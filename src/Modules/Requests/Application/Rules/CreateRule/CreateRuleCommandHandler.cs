using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Rules.CreateRule;

public class CreateRuleCommandHandler(IRequestsDbContext context)
    : IRequestHandler<CreateRuleCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateRuleCommand request, CancellationToken cancellationToken)
    {
        // Transition'ın var olduğunu kontrol et
        var transitionExists = await context.Transitions
            .AnyAsync(t => t.Id == request.TransitionId, cancellationToken);

        if (!transitionExists)
        {
            return Result.Failure<Guid>("Transition bulunamadı");
        }

        // Aynı order'da başka kural var mı kontrol et
        var orderExists = await context.TransitionRules
            .AnyAsync(r => r.TransitionId == request.TransitionId && r.Order == request.Order, cancellationToken);

        if (orderExists)
        {
            return Result.Failure<Guid>("Bu sıra numarasında başka bir kural zaten mevcut");
        }

        var rule = new TransitionRule
        {
            Id = Guid.NewGuid(),
            TransitionId = request.TransitionId,
            Name = request.Name,
            Description = request.Description,
            RuleType = request.RuleType,
            Order = request.Order,
            IsActive = request.IsActive,
            Configuration = request.Configuration
        };

        context.TransitionRules.Add(rule);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(rule.Id);
    }
}
