using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Rules.CreateRule;

public class CreateRuleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/transitions/{transitionId}/rules", async (
            Guid transitionId,
            CreateRuleRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new CreateRuleCommand(
                transitionId,
                request.Name,
                request.Description,
                request.RuleType,
                request.Order,
                request.IsActive,
                request.Configuration
            );

            var result = await mediator.Send(command, cancellationToken);

            return result.IsSuccess
                ? Results.Created($"/api/v1/requests/rules/{result.Value}", new { Id = result.Value })
                : Results.BadRequest(result.Error);
        })
        .WithName("CreateRule")
        .WithTags("Rules")
        .WithOpenApi();
    }
}

public record CreateRuleRequest(
    string Name,
    string Description,
    Domain.Enums.RuleType RuleType,
    int Order,
    bool IsActive,
    string Configuration
);
