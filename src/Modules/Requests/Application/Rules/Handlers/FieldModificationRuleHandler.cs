using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Configurations;
using Requests.Application.Rules.Engine;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Handlers;

public class FieldModificationRuleHandler(
    ILogger<FieldModificationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.FieldModification;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var config = GetConfiguration<FieldModificationConfiguration>(rule);
            var result = RuleResult.Success();

            foreach (var modification in config.Modifications)
            {
                var newValue = await CalculateNewValueAsync(modification, context);
                result.ModifiedFields[modification.FieldName] = newValue;
                
                logger.LogInformation("Field {FieldName} modified to {NewValue} for ticket {TicketId}", 
                    modification.FieldName, newValue, context.TicketId);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Field modification rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("<PERSON> sırasında hata oluştu");
        }
    }

    private async Task<object> CalculateNewValueAsync(FieldModification modification, TransitionContext context)
    {
        await Task.CompletedTask;

        return modification.Type switch
        {
            ModificationType.SetValue => modification.Value,
            ModificationType.SetCurrentDateTime => DateTime.UtcNow,
            ModificationType.SetCurrentUser => context.UserId,
            ModificationType.SetCalculatedValue => CalculateValue(modification.Value, context),
            _ => modification.Value
        };
    }

    private object CalculateValue(string expression, TransitionContext context)
    {
        // Template değişkenlerini değiştir
        var result = expression
            .Replace("{{NOW}}", DateTime.UtcNow.ToString())
            .Replace("{{CURRENT_USER_ID}}", context.UserId.ToString())
            .Replace("{{TICKET_ID}}", context.TicketId.ToString())
            .Replace("{{FROM_NODE_ID}}", context.FromNode.Id.ToString())
            .Replace("{{TO_NODE_ID}}", context.ToNode.Id.ToString());

        // Ticket alanlarından değer al
        foreach (var kvp in context.TicketData)
        {
            result = result.Replace($"{{{{TICKET.{kvp.Key}}}}}", kvp.Value?.ToString() ?? string.Empty);
        }

        // User alanlarından değer al
        foreach (var kvp in context.UserData)
        {
            result = result.Replace($"{{{{USER.{kvp.Key}}}}}", kvp.Value?.ToString() ?? string.Empty);
        }

        return result;
    }
}
