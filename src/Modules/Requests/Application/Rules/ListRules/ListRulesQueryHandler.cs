using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Rules.ListRules;

public class ListRulesQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListRulesQuery, Result<PagedResult<RuleListItemDto>>>
{
    public async Task<Result<PagedResult<RuleListItemDto>>> Handle(ListRulesQuery request, CancellationToken cancellationToken)
    {
        var query = context.TransitionRules.AsQueryable();

        if (request.TransitionId.HasValue)
        {
            query = query.Where(r => r.TransitionId == request.TransitionId.Value);
        }

        if (request.RuleType.HasValue)
        {
            query = query.Where(r => r.RuleType == request.RuleType.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(r => r.IsActive == request.IsActive.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var rules = await query
            .OrderBy(r => r.Order)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(r => new RuleListItemDto(
                r.Id,
                r.TransitionId,
                r.Name,
                r.Description,
                r.RuleType,
                r.Order,
                r.IsActive,
                r.InsertDate,
                r.UpdateDate
            ))
            .ToListAsync(cancellationToken);

        var pagedResult = new PagedResult<RuleListItemDto>(
            rules,
            totalCount,
            request.PageNumber,
            request.PageSize
        );

        return Result.Success(pagedResult);
    }
}
