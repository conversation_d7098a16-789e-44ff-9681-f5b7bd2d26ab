using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Requests.Domain.Enums;
using Shared.Endpoints;

namespace Requests.Application.Rules.ListRules;

public class ListRulesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/transitions/{transitionId}/rules", async (
            Guid transitionId,
            RuleType? ruleType,
            bool? isActive,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRulesQuery(
                transitionId,
                ruleType,
                isActive,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );

            var result = await mediator.Send(query, cancellationToken);

            return result.IsSuccess
                ? Results.Ok(result.Value)
                : Results.BadRequest(result.Error);
        })
        .WithName("ListRulesByTransition")
        .WithTags("Rules")
        .WithOpenApi();

        app.MapGet("/api/v1/requests/rules", async (
            RuleType? ruleType,
            bool? isActive,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRulesQuery(
                null,
                ruleType,
                isActive,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );

            var result = await mediator.Send(query, cancellationToken);

            return result.IsSuccess
                ? Results.Ok(result.Value)
                : Results.BadRequest(result.Error);
        })
        .WithName("ListAllRules")
        .WithTags("Rules")
        .WithOpenApi();
    }
}
