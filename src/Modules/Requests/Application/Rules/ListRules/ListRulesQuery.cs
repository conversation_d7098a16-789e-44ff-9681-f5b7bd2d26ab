using MediatR;
using Requests.Domain.Enums;
using Shared.Application;

namespace Requests.Application.Rules.ListRules;

public record ListRulesQuery(
    Guid? TransitionId = null,
    RuleType? RuleType = null,
    bool? IsActive = null,
    int PageNumber = 1,
    int PageSize = 10
) : IRequest<Result<PagedResult<RuleListItemDto>>>;

public record RuleListItemDto(
    Guid Id,
    Guid TransitionId,
    string Name,
    string Description,
    RuleType RuleType,
    int Order,
    bool IsActive,
    DateTime InsertDate,
    DateTime? UpdateDate
);
