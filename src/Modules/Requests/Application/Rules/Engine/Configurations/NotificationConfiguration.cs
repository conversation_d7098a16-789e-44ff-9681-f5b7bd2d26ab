namespace Requests.Application.Rules.Engine.Configurations;

public class NotificationConfiguration
{
    public List<NotificationDefinition> Notifications { get; set; } = [];
}

public class NotificationDefinition
{
    public NotificationType Type { get; set; }
    public RecipientDefinition Recipients { get; set; } = new();
    public string Template { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string? Condition { get; set; }
    public int DelaySeconds { get; set; } = 0;
}

public class RecipientDefinition
{
    public RecipientType Type { get; set; }
    public string? FieldName { get; set; }
    public List<string>? Roles { get; set; }
    public List<string>? Users { get; set; }
}

public enum NotificationType
{
    Email = 1,
    SMS = 2,
    InApp = 3,
    Push = 4
}

public enum RecipientType
{
    Field = 1,      // Ticket'taki bir alandan alınacak
    Role = 2,       // Bel<PERSON>li rollerdeki kullanıcılar
    User = 3,       // <PERSON><PERSON><PERSON> kullanıcılar
    CurrentUser = 4 // Mevcut kullanıcı
}
