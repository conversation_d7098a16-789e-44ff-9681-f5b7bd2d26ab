namespace Requests.Application.Rules.Engine.Configurations;

public class FieldValidationConfiguration
{
    public List<FieldValidationRule> ValidationRules { get; set; } = [];
}

public class FieldValidationRule
{
    public string FieldName { get; set; } = string.Empty;
    public ValidationType ValidationType { get; set; }
    public string? Value { get; set; }
    public string? Pattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public bool IsRequired { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}

public enum ValidationType
{
    Required = 1,
    MinLength = 2,
    <PERSON>Length = 3,
    <PERSON><PERSON> = 4,
    Equals = 5,
    NotEquals = 6,
    GreaterThan = 7,
    <PERSON><PERSON>han = 8,
    Custom = 99
}
