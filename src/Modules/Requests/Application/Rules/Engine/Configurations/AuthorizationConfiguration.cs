namespace Requests.Application.Rules.Engine.Configurations;

public class AuthorizationConfiguration
{
    public List<string>? AllowedRoles { get; set; }
    public List<string>? AllowedUsers { get; set; }
    public List<string>? RequiredPermissions { get; set; }
    public DepartmentRestriction? DepartmentRestriction { get; set; }
}

public class DepartmentRestriction
{
    public string Field { get; set; } = string.Empty;
    public bool AllowSameDepartment { get; set; }
    public List<string>? AllowedDepartments { get; set; }
}
