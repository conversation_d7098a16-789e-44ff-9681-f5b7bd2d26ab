using System.Diagnostics;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Requests.Application.Abstractions;
using Requests.Application.Rules.Engine.Handlers;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine;

public class RuleEngine(
    IServiceProvider serviceProvider,
    IRequestsDbContext dbContext,
    ILogger<RuleEngine> logger
) : IRuleEngine
{
    public async Task<RuleExecutionResult> ExecuteRulesAsync(
        Guid transitionId,
        TransitionContext context,
        CancellationToken cancellationToken = default)
    {
        var rules = await GetActiveRulesAsync(transitionId, cancellationToken);
        var result = new RuleExecutionResult();
        foreach (var rule in rules.OrderBy(r => r.Order))
        {
            var stopwatch = Stopwatch.StartNew();
            var executionId = Guid.NewGuid();
            try
            {
                var handler = GetRuleHandler(rule.RuleType);
                var ruleResult = await handler.ExecuteAsync(rule, context, cancellationToken);
                stopwatch.Stop();
                await LogRuleExecutionAsync(rule, context, executionId, ruleResult, stopwatch.ElapsedMilliseconds);
                if (!ruleResult.IsSuccess)
                {
                    result.Errors.AddRange(ruleResult.Errors);
                    if (ruleResult.IsBlockingFailure)
                    {
                        break;
                    }
                }
                result.ModifiedFields = MergeDictionaries(result.ModifiedFields, ruleResult.ModifiedFields);
                result.PendingNotifications.AddRange(ruleResult.PendingNotifications);
                result.Warnings.AddRange(ruleResult.Warnings);
                result.ExecutedRuleCount++;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                logger.LogError(ex, "Rule execution failed for rule {RuleId}", rule.Id);
                await LogRuleExecutionErrorAsync(rule, context, executionId, ex, stopwatch.ElapsedMilliseconds);
                result.Errors.Add($"Kural çalıştırma hatası: {rule.Name}");
            }
        }
        result.IsSuccess = !result.Errors.Any();
        return result;
    }

    public async Task<RuleValidationResult> ValidateRulesAsync(
        Guid transitionId,
        TransitionContext context,
        CancellationToken cancellationToken = default)
    {
        var rules = await GetActiveRulesAsync(transitionId, cancellationToken);
        var result = new RuleValidationResult { IsValid = true };

        foreach (var rule in rules.OrderBy(r => r.Order))
        {
            try
            {
                var handler = GetRuleHandler(rule.RuleType);
                var ruleResult = await handler.ExecuteAsync(rule, context, cancellationToken);

                if (!ruleResult.IsSuccess)
                {
                    result.ValidationErrors.AddRange(ruleResult.Errors);
                    if (ruleResult.IsBlockingFailure)
                    {
                        result.IsValid = false;
                    }

                }

                result.Warnings.AddRange(ruleResult.Warnings);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Rule validation failed for rule {RuleId}", rule.Id);
                result.ValidationErrors.Add($"Kural doğrulama hatası: {rule.Name}");
                result.IsValid = false;
            }
        }

        return result;
    }

    private async Task<List<TransitionRule>> GetActiveRulesAsync(Guid transitionId, CancellationToken cancellationToken)
    {
        return await dbContext.TransitionRules
            .Where(r => r.TransitionId == transitionId && r.IsActive)
            .OrderBy(r => r.Order)
            .ToListAsync(cancellationToken);
    }

    private BaseRuleHandler GetRuleHandler(RuleType ruleType)
    {
        return ruleType switch
        {
            RuleType.Authorization => serviceProvider.GetRequiredService<AuthorizationRuleHandler>(),
            RuleType.FieldValidation => serviceProvider.GetRequiredService<FieldValidationRuleHandler>(),
            RuleType.FieldModification => serviceProvider.GetRequiredService<FieldModificationRuleHandler>(),
            RuleType.Notification => serviceProvider.GetRequiredService<NotificationRuleHandler>(),
            _ => throw new NotSupportedException($"Rule type {ruleType} is not supported")
        };
    }

    private async Task LogRuleExecutionAsync(
        TransitionRule rule,
        TransitionContext context,
        Guid executionId,
        RuleResult ruleResult,
        long executionTimeMs)
    {
        var execution = new RuleExecution
        {
            Id = executionId,
            TransitionRuleId = rule.Id,
            TicketId = context.TicketId,
            ExecutedByUserId = context.UserId,
            ExecutedAt = DateTime.UtcNow,
            Status = ruleResult.IsSuccess ? RuleExecutionStatus.Success : RuleExecutionStatus.Failed,
            InputData = JsonSerializer.Serialize(new { context.TicketData, context.UserData, context.Metadata }),
            OutputData = JsonSerializer.Serialize(new { ruleResult.ModifiedFields, ruleResult.PendingNotifications }),
            ErrorMessage = string.Join(", ", ruleResult.Errors),
            ExecutionTimeMs = (int)executionTimeMs
        };
        dbContext.RuleExecutions.Add(execution);
        await dbContext.SaveChangesAsync();
    }

    private async Task LogRuleExecutionErrorAsync(
        TransitionRule rule,
        TransitionContext context,
        Guid executionId,
        Exception ex,
        long executionTimeMs)
    {
        var execution = new RuleExecution
        {
            Id = executionId,
            TransitionRuleId = rule.Id,
            TicketId = context.TicketId,
            ExecutedByUserId = context.UserId,
            ExecutedAt = DateTime.UtcNow,
            Status = RuleExecutionStatus.Failed,
            InputData = JsonSerializer.Serialize(new { context.TicketData, context.UserData, context.Metadata }),
            OutputData = "{}",
            ErrorMessage = ex.Message,
            ExecutionTimeMs = (int)executionTimeMs
        };

        dbContext.RuleExecutions.Add(execution);
        await dbContext.SaveChangesAsync();
    }

    private static Dictionary<string, object> MergeDictionaries(
        Dictionary<string, object> dict1,
        Dictionary<string, object> dict2)
    {
        var result = new Dictionary<string, object>(dict1);
        foreach (var kvp in dict2)
        {
            result[kvp.Key] = kvp.Value;
        }
        return result;
    }
}
