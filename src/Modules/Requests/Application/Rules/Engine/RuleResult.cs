namespace Requests.Application.Rules.Engine;

public class RuleResult
{
    public bool IsSuccess { get; set; }
    public bool IsBlockingFailure { get; set; }
    public List<string> Errors { get; set; } = [];
    public List<string> Warnings { get; set; } = [];
    public Dictionary<string, object> ModifiedFields { get; set; } = new();
    public List<NotificationRequest> PendingNotifications { get; set; } = [];

    public static RuleResult Success() => new() { IsSuccess = true };

    public static RuleResult Failure(string error, bool isBlocking = false) => new()
    {
        IsSuccess = false,
        IsBlockingFailure = isBlocking,
        Errors = [error]
    };

    public static RuleResult Warning(string warning) => new()
    {
        IsSuccess = true,
        Warnings = [warning]
    };
}

public class RuleExecutionResult
{
    public bool IsSuccess { get; set; }
    public List<string> Errors { get; set; } = [];
    public List<string> Warnings { get; set; } = [];
    public Dictionary<string, object> ModifiedFields { get; set; } = new();
    public List<NotificationRequest> PendingNotifications { get; set; } = [];
    public int ExecutedRuleCount { get; set; }
}

public class RuleValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = [];
    public List<string> Warnings { get; set; } = [];
}

public class NotificationRequest
{
    public string Type { get; set; } = string.Empty;
    public List<Guid> Recipients { get; set; } = [];
    public string Template { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public int DelaySeconds { get; set; }
}

public class TransitionExecutionResult
{
    public Guid TransitionId { get; set; }
    public Guid NewNodeId { get; set; }
    public Dictionary<string, object> ModifiedFields { get; set; } = new();
    public int NotificationsSent { get; set; }
    public int ExecutedRules { get; set; }
}
