using Requests.Domain;

namespace Requests.Application.Rules.Engine;

public class TransitionContext
{
    public Guid TicketId { get; set; }
    public Guid UserId { get; set; }
    public Dictionary<string, object> TicketData { get; set; } = [];
    public Dictionary<string, object> UserData { get; set; } = [];
    public Dictionary<string, object> Metadata { get; set; } = [];
    public Node FromNode { get; set; } = null!;
    public Node ToNode { get; set; } = null!;
    public DateTime TransitionTime { get; set; } = DateTime.UtcNow;

    // Helper methods
    public T? GetTicketField<T>(string fieldName) =>
        TicketData.TryGetValue(fieldName, out var value) ? (T?)value : default;

    public T? GetUserField<T>(string fieldName) =>
        UserData.TryGetValue(fieldName, out var value) ? (T?)value : default;

    public void SetTicketField(string fieldName, object value) =>
        TicketData[fieldName] = value;
}
