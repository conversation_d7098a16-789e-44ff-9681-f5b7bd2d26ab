using System.Text.Json;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public abstract class BaseRuleHandler
{
    public abstract RuleType RuleType { get; }

    public abstract Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken);

    protected T GetConfiguration<T>(TransitionRule rule) where T : class
    {
        return JsonSerializer.Deserialize<T>(rule.Configuration)
               ?? throw new InvalidOperationException($"Invalid configuration for rule {rule.Id}");
    }
}
