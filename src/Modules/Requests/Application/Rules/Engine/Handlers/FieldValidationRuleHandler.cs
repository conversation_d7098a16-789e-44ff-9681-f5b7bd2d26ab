using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Engine.Configurations;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public class FieldValidationRuleHandler(
    ILogger<FieldValidationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.FieldValidation;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var config = GetConfiguration<FieldValidationConfiguration>(rule);
            var errors = new List<string>();

            foreach (var validationRule in config.ValidationRules)
            {
                var fieldValue = context.GetTicketField<object>(validationRule.FieldName);
                var validationResult = await ValidateFieldAsync(validationRule, fieldValue);

                if (!validationResult.IsValid)
                {
                    errors.Add(validationResult.ErrorMessage);
                }
            }

            if (errors.Any())
            {
                return RuleResult.Failure(string.Join(", ", errors), isBlocking: true);
            }

            return RuleResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Field validation rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("Alan doğrulama sırasında hata oluştu", isBlocking: true);
        }
    }

    private async Task<ValidationResult> ValidateFieldAsync(FieldValidationRule rule, object? fieldValue)
    {
        await Task.CompletedTask;

        var stringValue = fieldValue?.ToString() ?? string.Empty;

        return rule.ValidationType switch
        {
            ValidationType.Required => ValidateRequired(rule, fieldValue),
            ValidationType.MinLength => ValidateMinLength(rule, stringValue),
            ValidationType.MaxLength => ValidateMaxLength(rule, stringValue),
            ValidationType.Pattern => ValidatePattern(rule, stringValue),
            ValidationType.Equals => ValidateEquals(rule, stringValue),
            ValidationType.NotEquals => ValidateNotEquals(rule, stringValue),
            _ => new ValidationResult { IsValid = true }
        };
    }

    private ValidationResult ValidateRequired(FieldValidationRule rule, object? fieldValue)
    {
        if (rule.IsRequired && (fieldValue == null || string.IsNullOrWhiteSpace(fieldValue.ToString())))
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı zorunludur"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateMinLength(FieldValidationRule rule, string value)
    {
        if (rule.MinLength.HasValue && value.Length < rule.MinLength.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı en az {rule.MinLength} karakter olmalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateMaxLength(FieldValidationRule rule, string value)
    {
        if (rule.MaxLength.HasValue && value.Length > rule.MaxLength.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı en fazla {rule.MaxLength} karakter olmalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidatePattern(FieldValidationRule rule, string value)
    {
        if (!string.IsNullOrEmpty(rule.Pattern) && !Regex.IsMatch(value, rule.Pattern))
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı geçerli formatta değil"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateEquals(FieldValidationRule rule, string value)
    {
        if (!string.IsNullOrEmpty(rule.Value) && value != rule.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı '{rule.Value}' değerine eşit olmalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateNotEquals(FieldValidationRule rule, string value)
    {
        if (!string.IsNullOrEmpty(rule.Value) && value == rule.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı '{rule.Value}' değerine eşit olmamalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
