namespace Requests.Application.Rules.Configurations;

public class FieldModificationConfiguration
{
    public List<FieldModification> Modifications { get; set; } = [];
}

public class FieldModification
{
    public string FieldName { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public ModificationType Type { get; set; }
}

public enum ModificationType
{
    SetValue = 1,
    SetCurrentDateTime = 2,
    SetCurrentUser = 3,
    SetCalculatedValue = 4
}
