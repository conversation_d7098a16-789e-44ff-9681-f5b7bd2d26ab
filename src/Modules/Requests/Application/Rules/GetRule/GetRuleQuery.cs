using MediatR;
using Requests.Domain.Enums;
using Shared.Application;

namespace Requests.Application.Rules.GetRule;

public record GetRuleQuery(Guid Id) : IRequest<Result<RuleDto>>;

public record RuleDto(
    Guid Id,
    Guid TransitionId,
    string Name,
    string Description,
    RuleType RuleType,
    int Order,
    bool IsActive,
    string Configuration,
    DateTime InsertDate,
    DateTime? UpdateDate
);
