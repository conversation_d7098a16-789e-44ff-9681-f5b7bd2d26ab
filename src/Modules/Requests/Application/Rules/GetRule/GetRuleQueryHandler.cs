using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Rules.GetRule;

public class GetRuleQueryHandler(IRequestsDbContext context)
    : IRequestHandler<GetRuleQuery, Result<RuleDto>>
{
    public async Task<Result<RuleDto>> Handle(GetRuleQuery request, CancellationToken cancellationToken)
    {
        var rule = await context.TransitionRules
            .FirstOrDefaultAsync(r => r.Id == request.Id, cancellationToken);

        if (rule is null)
        {
            return Result.Failure<RuleDto>("Kural bulunamadı");
        }

        var dto = new RuleDto(
            rule.Id,
            rule.TransitionId,
            rule.Name,
            rule.Description,
            rule.RuleType,
            rule.Order,
            rule.IsActive,
            rule.Configuration,
            rule.InsertDate,
            rule.UpdateDate
        );

        return Result.Success(dto);
    }
}
