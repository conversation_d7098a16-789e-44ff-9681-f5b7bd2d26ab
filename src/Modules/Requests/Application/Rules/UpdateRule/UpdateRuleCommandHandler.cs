using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Rules.UpdateRule;

public class UpdateRuleCommandHandler(IRequestsDbContext context)
    : IRequestHandler<UpdateRuleCommand, Result>
{
    public async Task<Result> Handle(UpdateRuleCommand request, CancellationToken cancellationToken)
    {
        var rule = await context.TransitionRules
            .FirstOrDefaultAsync(r => r.Id == request.Id, cancellationToken);

        if (rule is null)
        {
            return Result.Failure("Kural bulunamadı");
        }

        // Aynı transition'da aynı order'da başka kural var mı kontrol et (kendisi hariç)
        var orderExists = await context.TransitionRules
            .AnyAsync(r => r.TransitionId == rule.TransitionId && 
                          r.Order == request.Order && 
                          r.Id != request.Id, cancellationToken);

        if (orderExists)
        {
            return Result.Failure("Bu sıra numarasında başka bir kural zaten mevcut");
        }

        rule.Name = request.Name;
        rule.Description = request.Description;
        rule.RuleType = request.RuleType;
        rule.Order = request.Order;
        rule.IsActive = request.IsActive;
        rule.Configuration = request.Configuration;

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
