using FluentValidation;

namespace Requests.Application.Rules.UpdateRule;

public class UpdateRuleCommandValidator : AbstractValidator<UpdateRuleCommand>
{
    public UpdateRuleCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Kural ID gereklidir");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Kural adı gereklidir")
            .MaximumLength(200)
            .WithMessage("Kural adı en fazla 200 karakter olabilir");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Açıklama en fazla 1000 karakter olabilir");

        RuleFor(x => x.RuleType)
            .IsInEnum()
            .WithMessage("Geçerli bir kural tipi seçiniz");

        RuleFor(x => x.Order)
            .GreaterThan(0)
            .WithMessage("Sıra numarası 0'dan b<PERSON><PERSON><PERSON>k olmalıdır");

        RuleFor(x => x.Configuration)
            .NotEmpty()
            .WithMessage("Konfigürasyon gereklidir")
            .Must(BeValidJson)
            .WithMessage("Konfigürasyon geçerli bir JSON formatında olmalıdır");
    }

    private static bool BeValidJson(string json)
    {
        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
