namespace Requests.Application;

public class NodeDto
{
    public Guid Id { get; set; }
    public Guid FlowId { get; set; }
    public string FlowName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public List<TransitionDto> FromTransitions { get; set; } = new();
    public List<TransitionDto> ToTransitions { get; set; } = new();
}

public class NodeListItemDto
{
    public Guid Id { get; set; }
    public Guid FlowId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string NodeType { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public int FromTransitionCount { get; set; }
    public int ToTransitionCount { get; set; }
}
