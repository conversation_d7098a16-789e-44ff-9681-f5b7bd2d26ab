using Microsoft.EntityFrameworkCore;
using Requests.Domain;
using Shared.Infrastructure.Data;

namespace Requests.Application.Abstractions;

public interface IRequestsDbContext : IBaseDbContext
{
    DbSet<Ticket> Tickets { get; }
    DbSet<TicketDepartment> TicketDepartments { get; }
    DbSet<TicketSubject> TicketSubjects { get; }
    DbSet<TicketComment> TicketComments { get; }
    DbSet<TicketFile> TicketFiles { get; }
    DbSet<Flow> Flows { get; }
    DbSet<Node> Nodes { get; }
    DbSet<Transition> Transitions { get; }
    DbSet<TransitionRule> TransitionRules { get; }
    DbSet<RuleExecution> RuleExecutions { get; }
}
