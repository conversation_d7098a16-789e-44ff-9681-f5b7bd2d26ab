using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Nodes.ListNodes;

public class ListNodesQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListNodesQuery, Result<PagedResult<NodeDto>>>
{
    public async Task<Result<PagedResult<NodeDto>>> Handle(ListNodesQuery request, CancellationToken cancellationToken)
    {
        var query = context.Nodes.AsQueryable();

        if (request.FlowId.HasValue)
        {
            query = query.Where(n => n.FlowId == request.FlowId.Value);
        }

        if (request.Type.HasValue)
        {
            query = query.Where(n => n.NodeType == request.Type.Value);
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(n => n.Name.Contains(request.SearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var nodes = await query
            .Include(n => n.Flow)
            .OrderBy(n => n.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(n => new NodeDto
            {
                Id = n.Id,
                FlowId = n.FlowId,
                FlowName = n.Flow.Name,
                Name = n.Name,
                Description = n.Description,
                Type = n.NodeType.ToString(),
                InsertDate = n.InsertDate,
                UpdateDate = n.UpdateDate
            })
            .ToListAsync(cancellationToken);

        var pagedResult = new PagedResult<NodeDto>(nodes)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount
        };

        return Result.Success(pagedResult);
    }
}
